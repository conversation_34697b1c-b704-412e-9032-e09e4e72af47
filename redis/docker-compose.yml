services:
  redis:
    image: redis
    restart: always
    labels:
      - traefik.enable=true
      - traefik.tcp.routers.redis-redis.service=redis-redis
      - traefik.tcp.services.redis-redis.loadbalancer.server.port=6379
      - traefik.tcp.routers.redis-redis.rule=HostSNI(`${REDIS_HOST}`)
        # - traefik.tcp.routers.redis-redis.tls.passthrough=true
      - traefik.tcp.routers.redis-redis.entrypoints=redis
      - traefik.tcp.routers.redis-redis.tls=true
      - traefik.tcp.routers.redis-redis.tls.certresolver=production
    env_file:
      - .env
    volumes:
      - redis_data:/data #  Mounts the named volume to /data in container
    #   - ./database_volume:/var/lib/postgresql/data
    networks:
      - traefik_default

networks:
  traefik_default:
    external: true


volumes:
  redis_data: # Declares the named volume at compose level