services:
  traefik:
    image: traefik:v3.0.3
    restart: always
    # Enables the web UI and tells <PERSON><PERSON><PERSON><PERSON> to listen to docker
    command:
      - --configFile=/root/.config/traefik.yaml
      - --log.level=DEBUG
      - --api.dashboard=true
      - --metrics.prometheus=true
      - --metrics.prometheus.buckets=0.1,0.3,1.2,5.0
      - --providers.docker=true
      - --providers.docker.watch 
    labels:
      # This service is discoverable
      - traefik.enable=true
      - traefik.http.services.traefik-traefik.loadBalancer.server.port=8080
      - traefik.http.routers.traefik-traefik.rule=Host(`${TRAEFIK_HOST}`)
      - traefik.http.routers.traefik-traefik.tls=true
      - traefik.http.routers.traefik-traefik.entrypoints=websecure
      - traefik.http.routers.traefik-traefik.tls.certresolver=production
      # Ensure the service uses the correct path for the API
      - traefik.http.routers.traefik-traefik.service=api@internal
      # Authentication middleware
      - traefik.http.middlewares.auth-middleware.basicAuth.usersFile=/root/auth/.htpasswd
      - traefik.http.routers.traefik-traefik.middlewares=auth-middleware@docker
    ports:
      # The HTTP port
      - 80:80
      - 443:443
      - 5432:5432
      - 6379:6379
      - 27017:27017
      # The Web UI
      - 8080:8080
      - 9090:9090
      # - 3000:3000
      # - 9093:9093
    volumes:
      # So that Traefik can listen to the Docker events
      - /var/run/docker.sock:/var/run/docker.sock
      # For static config
      - ./config:/root/.config/
      # For Files provider
      - ./files:/root/files
      # For logging
      - ./logs:/root/logs
      # For SSL
      - ./cert:/root/cert/
      # For auth
      - ./auth:/root/auth
    environment:
      - TZ=Asia/Bangkok
    env_file:
      - .env
    networks:
      - default

networks:
  default: