# Salmate-Deployment-Guide

This repository contains the deployment setup for the application using Docker, Traefik, and environment configurations.

- [Salmate-Deployment-Guide](#salmate-deployment-guide)
  - [To Deploy](#to-deploy)
    - [Prerequisites](#prerequisites)
    - [Setup Environment Variables](#setup-environment-variables)
      - [Frontend Service](#frontend-service)
      - [Backend Service](#backend-service)
      - [LLM Service](#llm-service)
    - [Build and Run Containers](#build-and-run-containers)
    - [Verify Deployment](#verify-deployment)
  - [Troubleshooting](#troubleshooting)

## To Deploy

### Prerequisites

Ensure you have the following installed on your deployment machine:

- Docker & Docker Compose
- A domain name configured with DNS pointing to the server
- SSL certificates (Let's Encrypt will be used via Traefik)

### Setup Environment Variables
```
cp file.env .env
cp file.openai.key .openai.key
```

Create a `.env` file in the root directory and configure the required values:

#### Frontend Service
`.env`
```sh
PUBLIC_BACKEND_URL=
PUBLIC_BUILD_VERSION=
ORIGIN=
...
```

#### Backend Service
`.env`
```sh
REDIS_HOST=
REDIS_PORT=
DB_HOST=
DB_PORT=
DB_NAME=
DB_USER=
DB_PASS=
LINE_ACCESS_TOKEN=
LINE_CHANNEL_SECRET=
LANGSERVE_SCHEME=
LANGSERVE_HOST=
LANGSERVE_PORT=
ALLOWED_HOSTS=
NEARLY_EXPIRED_DATE=
INACTIVE_TICKET_TIME_MINUTES=
AZURE_ACCOUNT_NAME=
AZURE_ACCOUNT_KEY=
AZURE_CONTAINER=
...
```

#### LLM Service
`.env`
```sh
# Set this to `"true"` if you want to enable `LangSmith`
# Then, provide the `LANGCHAIN_API_KEY` below with the `PROJECT
LANGCHAIN_TRACING_V2="false"
# LANGCHAIN_API_KEY="<YOUR-API-KEY>"  # Update to your API key
LANGCHAIN_PROJECT=
LANGCHAIN_ENDPOINT=
ALLOW_ORIGINS=
```

`.openai.key `

```sh
OPENAI_API_VERSION=
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_API_KEY=
```

### Build and Run Containers

1. Start the Traefik reverse proxy:

   ```sh
   docker compose up -d
   ```

2. Deploy the application container:

   ```sh
   docker compose up -d
   ```

### Verify Deployment

Check if the containers are running:

```sh
docker ps
```

## Backup and Restore Functionality

The admin scripts now include automatic backup and restore functionality for persistent data directories.

### Backup Process (cleanup.sh)

When running `cleanup.sh`, the script automatically:

1. **Checks for persistent data directories:**
   - `~/_traefik/cert` - Traefik SSL certificates
   - `~/monitoring/dev-grafana-data` - Grafana data
   - `~/monitoring/dev-prom-data` - Prometheus data

2. **Creates timestamped backups:**
   - Backups are stored in `~/backup/salmate_backup_YYYYMMDD_HHMMSS/`
   - File permissions and ownership are preserved
   - Backup metadata is saved in `backup_info.txt`

3. **Safe backup process:**
   - Only backs up directories that exist
   - Uses `sudo` if permission issues occur
   - Continues cleanup even if backup fails

### Restore Process (setup.sh)

During deployment, `setup.sh` automatically:

1. **Searches for available backups** in `~/backup/`
2. **Prompts user for confirmation** before restoring
3. **Restores data with preserved permissions** to original locations
4. **Creates directory structure** if it doesn't exist

### Manual Backup/Restore

You can also manually manage backups:

```bash
# Create a manual backup before cleanup
mkdir -p ~/backup/manual_backup_$(date +%Y%m%d_%H%M%S)
cp -a ~/_traefik/cert ~/backup/manual_backup_$(date +%Y%m%d_%H%M%S)/traefik_cert

# List available backups
ls -la ~/backup/

# Manually restore from a specific backup
cp -a ~/backup/salmate_backup_20240101_120000/traefik_cert ~/_traefik/cert
```

### Important Notes

- **Backup Safety:** Backups are created with timestamped directories to prevent overwrites
- **Permission Handling:** The scripts handle permission issues automatically using `sudo` when needed
- **Storage Space:** Monitor backup directory size as multiple backups can consume significant space
- **Service Restart:** After restoring data, you may need to restart affected services
- **Cleanup:** Old backups are not automatically removed - manage them manually as needed

### Troubleshooting Backup/Restore

- **Permission Denied:** Scripts automatically retry with `sudo` when needed
- **Insufficient Space:** Ensure adequate disk space in `~/backup/` directory
- **Corrupted Backup:** Check `backup_info.txt` for backup details and integrity
- **Service Issues:** Restart Docker services after restore: `docker compose restart`

