services:
  frontend:
    image: ghcr.io/ai-brainlab/salmate_frontend:${tag}
    command: npm run serve
    restart: always
      #command: sleep infinity
    platform: linux/amd64
    labels:
      # This service is discoverable
      - traefik.enable=true
      - traefik.http.services.frontend-salmate.loadBalancer.server.port=8000
      - traefik.http.routers.frontend-salmate.rule=Host(`${FRONTEND_HOST}`)
      - traefik.http.routers.frontend-salmate.tls=true
      - traefik.http.routers.frontend-salmate.entrypoints=websecure
      - traefik.http.routers.frontend-salmate.tls.certresolver=production
    env_file:
      - .env
    networks:
      - traefik_default
    volumes:
      - ./logs:/src/app/log

networks:
  traefik_default:
    external: true