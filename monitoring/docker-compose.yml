services:
  prometheus:
    image: prom/prometheus
    platform: linux/amd64     
    volumes:
      - ./prometheus/:/etc/prometheus/
      - ${PROM_VOLUME}:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.size=256MB'
    labels:
      # This service is discoverable
      - traefik.enable=true
      - "traefik.http.routers.prometheus.rule=Host(`${PROMETHEUS_HOST}`)"
      - "traefik.http.routers.prometheus.service=prometheus"
      - "traefik.http.services.prometheus.loadbalancer.server.port=9090"
      - traefik.http.routers.prometheus.tls=true
      - traefik.http.routers.prometheus.entrypoints=websecure
      - traefik.http.routers.prometheus.tls.certresolver=production    
    networks:
      - traefik_default
    env_file:
      - .env
    restart: unless-stopped
    logging:
      options:
        max-size: "10m"
        max-file: "3"
  
  alertmanager:
    image: prom/alertmanager:v0.23.0
    ports:
      - "9093:9093"
    restart: unless-stopped
    labels:
      - traefik.enable=true  
      - "traefik.http.routers.alertmanager.rule=Host(`${ALERTMANAGER_HOST}`)"  
      - "traefik.http.routers.alertmanager.service=alertmanager"  
      - "traefik.http.services.alertmanager.loadbalancer.server.port=9093"  
      - traefik.http.routers.alertmanager.tls=true  
      - traefik.http.routers.alertmanager.entrypoints=websecure  
      - traefik.http.routers.alertmanager.tls.certresolver=production  
    volumes:
      - ./alertmanager:/config
      #- alertmanager-data:/data

    command:
      --config.file=/config/alertmanager.yml --log.level=debug
    networks:
      - traefik_default

  # NOTE: Excluded from deployment, Use central Grafana instance
  # grafana:
  #   image: grafana/grafana
  #   depends_on:
  #     - prometheus
  #   volumes:
  #     - ${GRAFANA_VOLUME}:/var/lib/grafana
  #     - ./grafana/:/etc/grafana/
  #   environment:
  #     # GF_SECURITY_DISABLE_INITIAL_ADMIN_CREATION: "true"
  #     # GF_INSTALL_PLUGINS: grafana-piechart-panel
  #     GF_SECURITY_ADMIN_USER: "admin"
  #     GF_SECURITY_ADMIN_PASSWORD: "admin"
  #   labels:
  #     # This service is discoverable
  #     - traefik.enable=true
  #     - "traefik.http.routers.grafana.rule=Host(`${GRAFANA_HOST}`)"
  #     - "traefik.http.routers.grafana.service=grafana"
  #     - "traefik.http.services.grafana.loadbalancer.server.port=3000"
  #     - traefik.http.routers.grafana.tls=true
  #     - traefik.http.routers.grafana.entrypoints=websecure
  #     - traefik.http.routers.grafana.tls.certresolver=production
  #   networks:
  #     - traefik_default
  #   env_file:
  #     - .env
  #   restart: unless-stopped
  #   logging:
  #     options:
  #       max-size: "10m"
  #       max-file: "3"   

  postgresql-exporter:
    image: prometheuscommunity/postgres-exporter
    container_name: postgresql-exporter
    ports:
      - "9187:9187"
    environment:
      DATA_SOURCE_NAME: "postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${HOST_URL}/${POSTGRES_DB}?sslmode=disable"
    depends_on:
      prometheus:
        condition: service_started
    restart: unless-stopped
    env_file:
      - .env
    networks:
     - traefik_default
  
  # NOTE: Excluded from deployment
  # mongo-exporter:
  #   image: bitnami/mongodb-exporter:latest
  #   container_name: mongo-exporter
  #   ports:
  #     - "9216:9216"
  #   environment:
  #     - MONGODB_URI=mongodb://${MONGODB_USERNAME}:${MONGODB_PASSWORD}@${MONGODB_HOST}:${MONGODB_PORT:-27017}/?authSource=admin
  #   depends_on:
  #     prometheus:
  #       condition: service_started
  #   restart: unless-stopped
  #   env_file:
  #     - .env
  #   networks:
  #     - traefik_default
  
  redis_exporter:
    image: oliver006/redis_exporter
    restart: always
    environment:
      - REDIS_ADDR=redis:6379
    # env_file:
    #   - .env
    ports:
      - "9121:9121"  # Expose the metrics on port 9121
    networks:
      - traefik_default

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - traefik_default

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: cadvisor
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "9200:8080"
    restart: unless-stopped
    networks:
      - traefik_default

networks:
  traefik_default:
    external: true
