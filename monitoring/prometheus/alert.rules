groups:
- name: example
  rules:

  # Alert for any instance that is unreachable for >1 minutes.
  - alert: service_down
    expr: up == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Instance {{ $labels.instance }} down"
      description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minutes."

  - alert: high_load
    expr: node_load1 > count(count(node_cpu_seconds_total{mode="idle"}) by (cpu)) * 0.7
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "Instance {{ $labels.instance }} under high load"
      description: "{{ $labels.instance }} of job {{ $labels.job }} is under high load."