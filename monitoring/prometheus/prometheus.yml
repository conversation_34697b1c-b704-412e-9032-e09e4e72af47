global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: "salmate"

# Disable alerting here and use centralized alertmanager
# Rules and alerts are read from the specified file(s)
# rule_files:
# - "alert.rules"

# Alertmanager configuration
# alerting:
# alertmanagers:
# - static_configs:
# - targets:
# - alertmanager:9093

scrape_configs:
  - job_name: "prometheus"
    scrape_interval: 5s
    static_configs:
      - targets: ["localhost:9090"]
  - job_name: "traefik"
    metrics_path: /metrics
    static_configs:
      - targets: ["traefik:8082"]
  - job_name: "information"
    static_configs:
      - targets: ["faq:8000"]
  - job_name: "langgraph"
    static_configs:
      - targets: ["langgraph:8000"]
  - job_name: "intent"
    static_configs:
      - targets: ["intent:8000"]
  - job_name: "tool"
    static_configs:
      - targets: ["tool:8000"]
  - job_name: "postgresql"
    static_configs:
      - targets: ["postgresql-exporter:9187"]
  - job_name: "redis"
    static_configs:
      - targets: ["redis_exporter:9121"]
  - job_name: "node"
    static_configs:
      - targets: ["node-exporter:9100"]
  - job_name: "cadvisor"
    static_configs:
      - targets: ["cadvisor:8080"]
