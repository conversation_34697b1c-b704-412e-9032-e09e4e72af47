services:
  app:
    image: your-app:${tag}
    command: npm run serve
      #command: sleep infinity
    platform: linux/amd64
    labels:
      # This service is discoverable
      - traefik.enable=true
      - traefik.http.services.app-salmate.loadBalancer.server.port=8000
      - traefik.http.routers.app-salmate.rule=Host(`your-domain.com`)
      - traefik.http.routers.app-salmate.tls=true
      - traefik.http.routers.app-salmate.entrypoints=websecure
      - traefik.http.routers.app-salmate.tls.certresolver=production
    env_file:
      - .env
    networks:
      - traefik_default
    volumes:
      - ./logs:/src/app/log

networks:
  traefik_default:
    external: true