{"secrets": [{"filename": "traefik.env", "destinations": ["$HOME/_traefik/.env"]}, {"filename": "backend.env", "destinations": ["$HOME/backend/.env"]}, {"filename": "frontend.env", "destinations": ["$HOME/frontend/.env"]}, {"filename": "llm-information.env", "destinations": ["$HOME/llm/information/.env"]}, {"filename": "llm-default.env", "destinations": ["$HOME/llm/default/.env"]}, {"filename": "llm-intent.env", "destinations": ["$HOME/llm/intent/.env"]}, {"filename": "llm-langgraph.env", "destinations": ["$HOME/llm/langgraph/.env"]}, {"filename": "llm-tool.env", "destinations": ["$HOME/llm/tool/.env"]}, {"filename": "llm-postgres-db.env", "destinations": ["$HOME/llm/postgres-db/.env"]}, {"filename": "llm-postgres-state.env", "destinations": ["$HOME/llm/postgres-state/.env"]}, {"filename": "postgres.env", "destinations": ["$HOME/postgres/.env"]}, {"filename": "redis.env", "destinations": ["$HOME/redis/.env"]}, {"filename": "monitoring.env", "destinations": ["$HOME/monitoring/.env"]}, {"filename": "openai.key", "destinations": ["$HOME/llm/tool/.openai.key", "$HOME/llm/intent/.openai.key", "$HOME/llm/information/.openai.key", "$HOME/llm/default/.openai.key", "$HOME/llm/langgraph/.openai.key"]}]}