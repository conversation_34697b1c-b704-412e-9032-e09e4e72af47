#!/bin/bash
set -e
set -o pipefail

# ======================================================
# restart.sh - Safely restart Docker services
# ======================================================

# Color definitions for status messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
DOCKER_TIMEOUT=30

# All services in dependency order (name:directory)
ALL_SERVICES=(
    "traefik:${HOME}/_traefik"
    "postgres:${HOME}/postgres"
    "redis:${HOME}/redis"
    "llm/postgres-db:${HOME}/llm/postgres-db"
    "llm/postgres-state:${HOME}/llm/postgres-state"
    "llm/tool:${HOME}/llm/tool"
    "llm/information:${HOME}/llm/information"
    "llm/intent:${HOME}/llm/intent"
    "llm/langgraph:${HOME}/llm/langgraph"
    "backend:${HOME}/backend"
    "frontend:${HOME}/frontend"
    "monitoring:${HOME}/monitoring"
)

# Function to display colored status messages
log() {
    local level=$1
    local message=$2

    case $level in
        "info")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "warn")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "error")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        *)
            echo -e "$message"
            ;;
    esac
}

# Function to get current tag for a service
get_service_tag() {
    local service_dir="$1"

    if [ -f "$service_dir/tag" ]; then
        cat "$service_dir/tag" 2>/dev/null || echo ""
    else
        echo ""
    fi
}

# Function to get service status
get_service_status() {
    local service_dir="$1"

    if [ ! -d "$service_dir" ]; then
        echo "not found"
        return
    fi

    if [ ! -f "$service_dir/docker-compose.yml" ] && [ ! -f "$service_dir/docker-compose.yaml" ]; then
        echo "no compose"
        return
    fi

    # Check if any containers are running for this service
    local running_containers
    running_containers=$(cd "$service_dir" && docker compose ps --services --filter "status=running" 2>/dev/null | wc -l)

    if [ "$running_containers" -gt 0 ]; then
        echo "running"
    else
        echo "stopped"
    fi
}

# Function to display current service status
display_service_status() {
    echo -e "${BLUE}=== Current Service Status ===${NC}"
    echo ""

    for service_entry in "${ALL_SERVICES[@]}"; do
        local service_name="${service_entry%%:*}"
        local service_dir="${service_entry##*:}"
        local status=$(get_service_status "$service_dir")
        local tag=$(get_service_tag "$service_dir")

        local status_color=""
        case $status in
            "running")
                status_color="${GREEN}"
                ;;
            "stopped")
                status_color="${RED}"
                ;;
            *)
                status_color="${YELLOW}"
                ;;
        esac

        if [ -n "$tag" ]; then
            echo -e "- ${service_name} (tag: ${tag}): ${status_color}${status}${NC}"
        else
            echo -e "- ${service_name}: ${status_color}${status}${NC}"
        fi
    done
    echo ""
}

# Function to display menu options
display_menu() {
    echo -e "${BLUE}=== Available Restart Options ===${NC}"
    echo ""

    for i in "${!ALL_SERVICES[@]}"; do
        local service_name="${ALL_SERVICES[$i]%%:*}"
        echo "$((i+1))) ${service_name}"
    done

    echo "$((${#ALL_SERVICES[@]}+1))) Restart all services"
    echo "0) Exit"
    echo ""
}

# Function to get user choice
get_user_choice() {
    local max_option=$((${#ALL_SERVICES[@]}+1))
    local choice

    read -p "Select an option (0-${max_option}): " choice

    # Validate choice
    if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 0 ] && [ "$choice" -le "$max_option" ]; then
        echo "$choice"
        return 0
    else
        echo ""
        return 1
    fi
}

# Function to confirm action
confirm_action() {
    local message="$1"
    local response

    read -p "${message} (y/N): " response
    case "$response" in
        [yY]|[yY][eE][sS])
            return 0
            ;;
        *)
            return 1
            ;;
    esac
}

# Function to restart a single service
restart_single_service() {
    local service_name="$1"
    local service_dir="$2"

    log "info" "Restarting service: $service_name"

    if [ ! -d "$service_dir" ]; then
        log "error" "Service directory not found: $service_dir"
        return 1
    fi

    if [ ! -f "$service_dir/docker-compose.yml" ] && [ ! -f "$service_dir/docker-compose.yaml" ]; then
        log "error" "No docker-compose file found in: $service_dir"
        return 1
    fi

    # Stop the service
    log "info" "Stopping $service_name..."
    if ! (cd "$service_dir" && docker compose down --timeout $DOCKER_TIMEOUT); then
        log "error" "Failed to stop $service_name"
        return 1
    fi

    # Start the service
    log "info" "Starting $service_name..."
    if [ -f "$service_dir/tag" ]; then
        local service_tag=$(cat "$service_dir/tag" 2>/dev/null)
        if [ -n "$service_tag" ]; then
            log "info" "Using tag: $service_tag"
            if ! (cd "$service_dir" && export tag="$service_tag" && docker compose up -d); then
                log "error" "Failed to start $service_name with tag $service_tag"
                return 1
            fi
        else
            if ! (cd "$service_dir" && docker compose up -d); then
                log "error" "Failed to start $service_name"
                return 1
            fi
        fi
    else
        if ! (cd "$service_dir" && docker compose up -d); then
            log "error" "Failed to start $service_name"
            return 1
        fi
    fi

    log "info" "✅ Successfully restarted $service_name"
    return 0
}

# Function to check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        log "error" "Docker is not installed. Please install Docker first."
        log "info" "Visit https://docs.docker.com/get-docker/ for installation instructions."
        exit 1
    fi

    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log "error" "Docker daemon is not running. Please start Docker first."
        exit 1
    fi

    log "info" "Docker is installed and running."
}

# Function to check if Docker Compose is installed
check_docker_compose() {
    if ! docker compose version &> /dev/null; then
        log "error" "Docker Compose is not installed. Please install Docker Compose first."
        log "info" "Visit https://docs.docker.com/compose/install/ for installation instructions."
        exit 1
    fi

    log "info" "Docker Compose is installed."
}

# Function to stop all running containers
stop_containers() {
    log "info" "Stopping all running containers with a ${DOCKER_TIMEOUT}-second timeout..."

    # Stop services in reverse order
    for ((i=${#ALL_SERVICES[@]}-1; i>=0; i--)); do
        local service_entry="${ALL_SERVICES[$i]}"
        local service_name="${service_entry%%:*}"
        local service_dir="${service_entry##*:}"

        if [ -d "$service_dir" ] && [ -f "$service_dir/docker-compose.yml" -o -f "$service_dir/docker-compose.yaml" ]; then
            log "info" "Stopping $service_name..."
            (cd "$service_dir" && docker compose down --timeout $DOCKER_TIMEOUT) || log "warn" "Failed to stop $service_name"
        fi
    done

    log "info" "All containers stopped."
}

# Function to clean up Docker resources
clean_resources() {
    log "info" "Cleaning up Docker resources..."

    # Remove dangling volumes
    log "info" "Removing dangling volumes..."
    docker volume prune -f

    # Remove dangling networks
    log "info" "Removing dangling networks..."
    docker network prune -f

    # Remove dangling images
    log "info" "Removing dangling images..."
    docker image prune -f

    log "info" "Docker resources cleaned up."
}

# Function to start all services
start_services() {
    log "info" "Starting all services..."

    # Start services in order
    for service_entry in "${ALL_SERVICES[@]}"; do
        local service_name="${service_entry%%:*}"
        local service_dir="${service_entry##*:}"

        if [ -d "$service_dir" ] && [ -f "$service_dir/docker-compose.yml" -o -f "$service_dir/docker-compose.yaml" ]; then
            # Check if a tag file exists for this service
            if [ -f "$service_dir/tag" ]; then
                # Read the tag from the file
                local service_tag=$(cat "$service_dir/tag" 2>/dev/null)

                if [ -n "$service_tag" ]; then
                    log "info" "Starting $service_name with tag: $service_tag..."
                    (cd "$service_dir" && export tag="$service_tag" && docker compose up -d) || {
                        log "error" "Failed to start $service_name"
                        return 2
                    }
                else
                    log "warn" "Tag file exists for $service_name but is empty. Starting without tag..."
                    (cd "$service_dir" && docker compose up -d) || {
                        log "error" "Failed to start $service_name"
                        return 2
                    }
                fi
            else
                log "info" "Starting $service_name (no tag)..."
                (cd "$service_dir" && docker compose up -d) || {
                    log "error" "Failed to start $service_name"
                    return 2
                }
            fi
        fi
    done

    log "info" "All services started successfully."
    return 0
}

# Function for interactive mode
interactive_mode() {
    log "info" "Starting interactive restart menu..."

    # Check dependencies first
    check_docker
    check_docker_compose

    while true; do
        echo ""
        display_service_status
        display_menu

        local choice
        choice=$(get_user_choice)

        if [ $? -ne 0 ] || [ -z "$choice" ]; then
            log "error" "Invalid selection. Please try again."
            continue
        fi

        case $choice in
            0)
                log "info" "Exiting..."
                exit 0
                ;;
            $((${#ALL_SERVICES[@]}+1)))
                # Restart all services
                if confirm_action "This will restart ALL services. Continue?"; then
                    log "info" "Restarting all services..."
                    stop_containers
                    start_services
                    if [ $? -eq 0 ]; then
                        log "info" "✅ All services restarted successfully."
                    else
                        log "error" "❌ Some services failed to restart."
                    fi
                else
                    log "info" "Operation cancelled."
                fi
                ;;
            *)
                # Restart individual service
                local service_index=$((choice-1))
                local service_entry="${ALL_SERVICES[$service_index]}"
                local service_name="${service_entry%%:*}"
                local service_dir="${service_entry##*:}"

                if confirm_action "Restart $service_name?"; then
                    restart_single_service "$service_name" "$service_dir"
                else
                    log "info" "Operation cancelled."
                fi
                ;;
        esac

        echo ""
        read -p "Press Enter to continue..."
    done
}

# Function to display help/usage information
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Safely stop and restart Docker services defined in the project."
    echo ""
    echo "Options:"
    echo "  -h, --help     Display this help message and exit"
    echo "  -c, --clean    Clean up dangling volumes, networks, and images before restarting"
    echo ""
    echo "Examples:"
    echo "  $0             # Interactive menu mode"
    echo "  $0 --clean     # Clean up Docker resources and restart all services"
    echo "  $0 --help      # Display this help message"
    echo ""
    echo "Interactive Mode:"
    echo "  When run without arguments, the script enters interactive mode where you can:"
    echo "  - View current service status"
    echo "  - Restart individual services"
    echo "  - Restart all services at once"
    echo ""
    echo "Exit codes:"
    echo "  0 - Success"
    echo "  1 - Dependency error (Docker or Docker Compose not installed)"
    echo "  2 - Docker operation failure"
}

# Parse command-line arguments and determine mode
CLEAN=false
INTERACTIVE_MODE=true

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--clean)
            CLEAN=true
            INTERACTIVE_MODE=false
            shift
            ;;
        *)
            log "error" "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main script execution for command-line mode
main_cli() {
    log "info" "Starting service restart process..."

    # Check dependencies
    check_docker
    check_docker_compose

    # Stop all running containers
    stop_containers

    # Clean up resources if requested
    if [ "$CLEAN" = true ]; then
        clean_resources
    fi

    # Start all services
    start_services
    exit_code=$?

    if [ $exit_code -eq 0 ]; then
        log "info" "Service restart completed successfully."
    else
        log "error" "Service restart failed with exit code $exit_code."
        exit $exit_code
    fi
}

# Execute appropriate mode
if [ "$INTERACTIVE_MODE" = true ]; then
    interactive_mode
else
    main_cli
fi
