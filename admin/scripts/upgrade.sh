#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status
set -o pipefail  # Return value of a pipeline is the value of the last command to exit with non-zero status

# Ensure script is run with bash
if [ -z "$BASH_VERSION" ]; then
    echo "This script must be run with bash, not sh or other shells."
    exit 1
fi

# Function to log messages with timestamps
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to get current tag for a service
get_current_tag() {
    local service_dir="$1"

    if [ -f "$service_dir/tag" ]; then
        cat "$service_dir/tag"
    fi
}

# Define services that use tag versions
SERVICES=(
    "backend"
    "frontend"
    "llm/information"
    "llm/intent"
    "llm/langgraph"
    "llm/tool"
)

# Display current tags for all services
# Mapping SERVICES to their corresponding directories
log "Current service tags:"
for service in "${SERVICES[@]}"; do
    current_tag=$(get_current_tag "$HOME/$service")
    current_tag=${current_tag:-unknown}
    echo "- $service: $current_tag"
done

echo ""

# Display menu
echo "Available services:"
for i in "${!SERVICES[@]}"; do
    echo "$((i+1))) ${SERVICES[$i]}"
done
echo "0) Exit"
echo ""

# Get user selection
read -p "Select a service to redeploy (0-${#SERVICES[@]}): " choice

# Validate choice
if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 0 ] && [ "$choice" -le "${#SERVICES[@]}" ]; then
    if [ "$choice" -eq 0 ]; then
        log "Exiting..."
        exit 0
    else
        selected_service="${SERVICES[$((choice-1))]}"
    fi
else
    log "Invalid selection. Exiting."
    exit 1
fi

if [ -n "$selected_service" ]; then
    service_dir="$HOME/$selected_service"
    current_tag=$(get_current_tag "$service_dir")
    current_tag=${current_tag:-unknown}

    log "Selected service: $selected_service (current tag: $current_tag)"

    # Prompt for new tag
    read -p "Enter new tag version (e.g., v1.0.1): " new_tag

    if [ -z "$new_tag" ]; then
        log "No tag provided. Exiting."
        exit 1
    fi

    log "Validating tag: $new_tag for service: $selected_service"

    # Navigate to service directory
    cd "$service_dir"

    # Determine the image name from docker-compose.yml
    if [ -f "docker-compose.yml" ]; then
        compose_file="docker-compose.yml"
    elif [ -f "docker-compose.yaml" ]; then
        compose_file="docker-compose.yaml"
    else
        log "❌ Error: No docker-compose.yml or docker-compose.yaml found in $service_dir"
        exit 1
    fi

    # Extract image name from docker-compose file
    # This handles both "image: name:tag" and "image: name" formats
    image_line=$(grep -E "^\s*image:" "$compose_file" | head -n 1)
    image_name=$(echo "$image_line" | sed -E 's/^\s*image:\s*([^:$]+)(:.+)?$/\1/' | tr -d '"' | tr -d "'")

    if [ -z "$image_name" ]; then
        log "❌ Error: Could not determine image name from $compose_file"
        exit 1
    fi

    log "Detected image name: $image_name"

    # Check if the tag exists by attempting to pull it
    log "Pulling image if tag exists: $new_tag"
    if ! docker pull "$image_name:$new_tag"; then
        log "❌ Error: Tag '$new_tag' does not exist for image '$image_name'"
        log "Please check available tags and try again."
        exit 1
    fi

    log "✅ Tag validation successful"
    log "Redeploying $selected_service with tag: $new_tag"

    # Pull the new image (we already pulled it during validation)
    log "Image with tag: $new_tag already pulled"
    export tag="$new_tag"

    # Stop the current service
    log "Stopping current service..."
    if [ -f "tag" ]; then
        tag=$(cat tag) docker compose down
    else
        docker compose down
    fi

    # Start with new tag
    log "Starting service with new tag: $new_tag"
    export tag="$new_tag"
    docker compose up -d

    # Save the new tag
    echo "$new_tag" > tag

    # Delete old image after upgrading
    log "Deleting old image: $image_name:$current_tag"
    docker rmi "$image_name:$current_tag"

    log "✅ Service $selected_service redeployed successfully with tag: $new_tag"
else
    log "No service selected. Exiting."
fi