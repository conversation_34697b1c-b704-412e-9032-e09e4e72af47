{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"baseName": {"type": "string", "metadata": {"description": "Base name for resources (e.g., 'customer-app'). Combined with environment."}}, "environment": {"type": "string", "allowedValues": ["d", "t", "s", "p"], "metadata": {"description": "Deployment environment (d = dev, t = test, s = staging/uat, p = production)."}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Azure region for deployment. Defaults to the resource group location."}}, "adminUsername": {"type": "string", "defaultValue": "aeai-", "metadata": {"description": "Administrator username for the Linux VM."}}, "sshPublicKeyData": {"type": "securestring", "metadata": {"description": "SSH public key data (e.g., content of 'id_rsa.pub'). Generate key pair externally (ssh-keygen). Example: 'ssh-rsa AAAAB3N... user@host'"}}, "vmSize": {"type": "string", "defaultValue": "Standard_D2s_v3", "metadata": {"description": "Size of the Virtual Machine (e.g., Standard_D2s_v3, Standard_F4s_v2)."}}, "osDiskType": {"type": "string", "defaultValue": "Premium_LRS", "allowedValues": ["Standard_LRS", "Premium_LRS", "StandardSSD_LRS"], "metadata": {"description": "Type of the OS Managed Disk (Standard_LRS, Premium_LRS, StandardSSD_LRS)."}}, "ubuntuOSVersion": {"type": "string", "defaultValue": "22.04-LTS", "allowedValues": ["22.04-LTS", "20.04-LTS", "18.04-LTS"], "metadata": {"description": "Ubuntu LTS version for the VM (Gen2 image assumed)."}}, "existingVnetName": {"type": "string", "defaultValue": "salmate-vnet", "metadata": {"description": "Name of the existing Virtual Network to deploy into."}}, "existingSubnetName": {"type": "string", "defaultValue": "default", "metadata": {"description": "Name of the existing Subnet within the VNet."}}, "existingVnetResourceGroupName": {"type": "string", "defaultValue": "", "metadata": {"description": "Resource group name where the existing VNet resides. Leave empty to use the current resource group."}}, "availabilityZones": {"type": "array", "defaultValue": ["1"], "metadata": {"description": "Availability Zones for VM and Public IP. E.g., ['1'], ['1', '2'], or [] for non-zonal. Ensure VM size and region support zones."}}, "publicIpSku": {"type": "string", "defaultValue": "Standard", "allowedValues": ["Basic", "Standard"], "metadata": {"description": "SKU for the Public IP address (Basic or Standard). Standard is recommended and required for Zones."}}, "publicIpAllocationMethod": {"type": "string", "defaultValue": "Static", "allowedValues": ["Static", "Dynamic"], "metadata": {"description": "Public IP allocation method (Static or Dynamic)."}}, "sshSourceAddressPrefix": {"type": "string", "defaultValue": "*", "metadata": {"description": "The CIDR notation or IP address range allowed to connect via SSH. Use '*' for any source (less secure) or specify your IP range (e.g., 'YOUR_PUBLIC_IP/32')."}}}, "variables": {"resourceNamePrefix": "[concat(parameters('baseName'), '-', parameters('environment'))]", "vmName": "[concat(variables('resourceNamePrefix'), '-vm')]", "nicName": "[concat(variables('resourceNamePrefix'), '-nic')]", "publicIpName": "[concat(variables('resourceNamePrefix'), '-pip')]", "nsgName": "[concat(variables('resourceNamePrefix'), '-nsg')]", "osDiskName": "[concat(variables('vmName'), '-osdisk')]", "vnetResourceGroupName": "[if(empty(parameters('existingVnetResourceGroupName')), resourceGroup().name, parameters('existingVnetResourceGroupName'))]", "subnetId": "[resourceId(variables('vnetResourceGroupName'), 'Microsoft.Network/virtualNetworks/subnets', parameters('existingVnetName'), parameters('existingSubnetName'))]", "imageReference": {"publisher": "Canonical", "offer": "0001-com-ubuntu-server-jammy", "sku": "[concat(replace(parameters('ubuntuOSVersion'), '.', '_'), '-gen2')]", "version": "latest"}, "useZones": "[greater(length(parameters('availabilityZones')), 0)]", "vmZones": "[if(variables('useZones'), parameters('availabilityZones'), json('null'))]", "publicIpZones": "[if(and(variables('useZones'), equals(parameters('publicIpSku'), 'Standard')), parameters('availabilityZones'), json('null'))]"}, "resources": [{"type": "Microsoft.Network/networkSecurityGroups", "apiVersion": "2023-11-01", "name": "[variables('nsgName')]", "location": "[parameters('location')]", "properties": {"securityRules": [{"name": "SSH", "properties": {"description": "Allow SSH access", "protocol": "Tcp", "sourcePortRange": "*", "destinationPortRange": "22", "sourceAddressPrefix": "[parameters('sshSourceAddressPrefix')]", "destinationAddressPrefix": "*", "access": "Allow", "priority": 300, "direction": "Inbound"}}, {"name": "HTTP", "properties": {"description": "Allow HTTP access", "protocol": "Tcp", "sourcePortRange": "*", "destinationPortRange": "80", "sourceAddressPrefix": "[parameters('sshSourceAddressPrefix')]", "destinationAddressPrefix": "*", "access": "Allow", "priority": 310, "direction": "Inbound"}}, {"name": "HTTPS", "properties": {"description": "Allow HTTPS access", "protocol": "Tcp", "sourcePortRange": "*", "destinationPortRange": "443", "sourceAddressPrefix": "[parameters('sshSourceAddressPrefix')]", "destinationAddressPrefix": "*", "access": "Allow", "priority": 320, "direction": "Inbound"}}, {"name": "Traefik", "properties": {"description": "Allow Traefik access", "protocol": "Tcp", "sourcePortRange": "*", "destinationPortRange": "8080", "sourceAddressPrefix": "[parameters('sshSourceAddressPrefix')]", "destinationAddressPrefix": "*", "access": "Allow", "priority": 330, "direction": "Inbound"}}, {"name": "Postgres", "properties": {"description": "Allow Postgres access", "protocol": "Tcp", "sourcePortRange": "*", "destinationPortRange": "5432", "sourceAddressPrefix": "[parameters('sshSourceAddressPrefix')]", "destinationAddressPrefix": "*", "access": "Allow", "priority": 340, "direction": "Inbound"}}, {"name": "Redis", "properties": {"description": "Allow Redis access", "protocol": "Tcp", "sourcePortRange": "*", "destinationPortRange": "6379", "sourceAddressPrefix": "[parameters('sshSourceAddressPrefix')]", "destinationAddressPrefix": "*", "access": "Allow", "priority": 350, "direction": "Inbound"}}, {"name": "ICMP", "properties": {"description": "Allow ICMP access", "protocol": "Icmp", "sourcePortRange": "*", "destinationPortRange": "*", "sourceAddressPrefix": "[parameters('sshSourceAddressPrefix')]", "destinationAddressPrefix": "*", "access": "Allow", "priority": 360, "direction": "Inbound"}}]}}, {"type": "Microsoft.Network/publicIPAddresses", "apiVersion": "2023-11-01", "name": "[variables('publicIpName')]", "location": "[parameters('location')]", "sku": {"name": "[parameters('publicIpSku')]", "tier": "Regional"}, "zones": "[variables('publicIpZones')]", "properties": {"publicIPAllocationMethod": "[parameters('publicIpAllocationMethod')]"}}, {"type": "Microsoft.Network/networkInterfaces", "apiVersion": "2023-11-01", "name": "[variables('nicName')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Network/publicIPAddresses', variables('publicIpName'))]", "[resourceId('Microsoft.Network/networkSecurityGroups', variables('nsgName'))]"], "properties": {"ipConfigurations": [{"name": "ipconfig1", "properties": {"privateIPAllocationMethod": "Dynamic", "publicIPAddress": {"id": "[resourceId('Microsoft.Network/publicIPAddresses', variables('publicIpName'))]"}, "subnet": {"id": "[variables('subnetId')]"}, "primary": true, "privateIPAddressVersion": "IPv4"}}], "networkSecurityGroup": {"id": "[resourceId('Microsoft.Network/networkSecurityGroups', variables('nsgName'))]"}, "enableAcceleratedNetworking": true}}, {"type": "Microsoft.Compute/virtualMachines", "apiVersion": "2024-03-01", "name": "[variables('vmName')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Network/networkInterfaces', variables('nicName'))]"], "zones": "[variables('vmZones')]", "properties": {"hardwareProfile": {"vmSize": "[parameters('vmSize')]"}, "storageProfile": {"imageReference": "[variables('imageReference')]", "osDisk": {"osType": "Linux", "name": "[variables('osDiskName')]", "createOption": "FromImage", "caching": "ReadWrite", "managedDisk": {"storageAccountType": "[parameters('osDiskType')]"}, "deleteOption": "Delete", "diskSizeGB": 50}}, "osProfile": {"computerName": "[variables('vmName')]", "adminUsername": "[parameters('adminUsername')]", "linuxConfiguration": {"disablePasswordAuthentication": true, "ssh": {"publicKeys": [{"path": "[format('/home/<USER>/.ssh/authorized_keys', parameters('adminUsername'))]", "keyData": "[parameters('sshPublicKeyData')]"}]}, "provisionVMAgent": true}}, "securityProfile": {"uefiSettings": {"secureBootEnabled": true, "vTpmEnabled": true}, "securityType": "TrustedLaunch"}, "networkProfile": {"networkInterfaces": [{"id": "[resourceId('Microsoft.Network/networkInterfaces', variables('nicName'))]", "properties": {"deleteOption": "Delete"}}]}, "diagnosticsProfile": {"bootDiagnostics": {"enabled": true}}}}], "outputs": {"adminUsername": {"type": "string", "value": "[parameters('adminUsername')]"}, "publicIpAddress": {"type": "string", "value": "[reference(resourceId('Microsoft.Network/publicIPAddresses', variables('publicIpName')), '2023-11-01').ipAddress]"}, "vmId": {"type": "string", "value": "[resourceId('Microsoft.Compute/virtualMachines', variables('vmName'))]"}, "sshCommand": {"type": "string", "value": "[format('ssh {0}@{1}', parameters('adminUsername'), reference(resourceId('Microsoft.Network/publicIPAddresses', variables('publicIpName')), '2023-11-01').ipAddress)]"}}}