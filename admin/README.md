# Salmate Deployment Guide

This repository contains the deployment setup for the Salmate application using Docker, Traefik, and environment configurations.

## Directory Structure

```
admin
├── README.md
├── scripts
│   ├── cleanup.sh
│   ├── setup.sh
│   ├── restart.sh
│   └── upgrade.sh
├── secrets
│   ├── backend.env
│   ├── frontend.env
│   ├── llm-default.env
│   ├── llm-information.env
│   ├── llm-intent.env
│   ├── llm-langgraph.env
│   ├── llm-postgres-db.env
│   ├── llm-postgres-state.env
│   ├── llm-tool.env
│   ├── mongodb.env
│   ├── monitoring.env
│   ├── openai.key
│   ├── postgres.env
│   ├── redis.env
│   ├── secrets-map.json
│   └── traefik.env
└── azure-vm-template
    └── arm.json
```

## Secrets Management

The `secrets/` directory contains environment files for all services. Each file must be properly configured before deployment:

| File | Purpose |
|------|---------|
| backend.env | Django backend configuration |
| frontend.env | Svelte frontend settings |
| llm-*.env | Configuration for various LLM services |
| mongodb.env | MongoDB connection settings |
| postgres.env | PostgreSQL database configuration |
| redis.env | Redis cache settings |
| openai.key | OpenAI API credentials |
| traefik.env | Traefik reverse proxy configuration |

## Deployment Guide

### 1. Deploy Azure VM using ARM Template

   - Use the `azure-vm-template/arm.json` template to deploy the VM

### 2. Deployment

1. **Prepare files**
   - Place script and secret files in the `~/admin` directory on the VM
   - These files will be used for configuration during deployment

2. **Run the setup script**
   ```sh
   ./admin/scripts/setup.sh
   ```

   This script performs the following steps:
   - Sets up the server environment (timezone, packages)
   - Clones the repository
      - Source: https://github.com/AI-Brainlab/salmate-production.git
   - Deploys configuration files to their proper locations
   - Starts all required Docker containers in the correct order using tag v1.0.0:
     - Traefik (reverse proxy)
     - Postgres and Redis (databases)
     - LLM services (tool, langgraph, etc.)
     - Backend service (with automatic database migrations)
     - Frontend service
     - Monitoring services

### 3. Managing Your Deployment

#### Updating Secret Environment Variables

If you need to modify secret environment variables and redeploy:

1. **Clean up the current deployment**
   ```sh
   ./admin/scripts/cleanup.sh
   ```
   This removes all deployed files, containers, and configurations

2. **Modify your secret files** as needed

3. **Run the setup script again**
   ```sh
   ./admin/scripts/setup.sh
   ```

#### Upgrading a Service with a Newer Tag

To update a specific service with a newer Docker image tag:

1. **Run the upgrade script**
   ```sh
   ./admin/scripts/upgrade.sh
   ```

2. **Select the service** you want to update from the menu

3. **Enter the new tag version** when prompted

The script will:
- Validate that the tag exists
- Pull the new image
- Stop the current service
- Start the service with the new tag
- Save the tag information for future reference

### 4. Service Management

To safely stop and restart all Docker services, use the restart script:

```sh
./admin/scripts/restart.sh
```

This script will:
- Check if Docker and Docker Compose are installed
- Gracefully stop all running containers with a 30-second timeout
- Restart all services in the correct order
- Display color-coded status messages throughout the process

#### Options

| Option | Description |
|--------|-------------|
| `-h, --help` | Display help information and usage examples |
| `-c, --clean` | Clean up dangling volumes, networks, and images before restarting |

#### Examples

Basic restart (preserves all data):
```sh
./admin/scripts/restart.sh
```

Restart with cleanup (removes unused resources):
```sh
./admin/scripts/restart.sh --clean
```

#### Prerequisites

- Docker and Docker Compose must be installed
- The script must be run from the project root directory
- The user must have permission to run Docker commands

#### Troubleshooting

- If services fail to start, check the Docker logs: `docker logs <container_name>`
- If you encounter permission issues, ensure your user is in the Docker group
- For network conflicts, use the `--clean` option to remove dangling networks

### 5. DNS Configuration

Follow this guide to manually register new DNS in Squarespace:
  https://support.squarespace.com/hc/en-us/articles/************-Adding-DNS-records-to-your-domain

## Deployment Workflow

<img src="images/deployment-flow.png" width="70%" alt="Deployment Flow">

## Version History

| Version | Date | Changes |
|---------|------|---------|
| v1.0.1  | 2023-05-21 | Added restart.sh script for service management |
| v1.0.0  | 2023-05-09 | Initial release |