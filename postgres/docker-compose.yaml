services:
  db:
    image: postgres
    restart: always
    # set shared memory limit when using docker-compose
    shm_size: 128mb
    # or set shared memory limit when deploy via swarm stack
    #volumes:
    #  - type: tmpfs
    #    target: /dev/shm
    #    tmpfs:
    #      size: 134217728 # 128*2^20 bytes = 128Mb
    # environment:
    #   POSTGRES_PASSWORD: example
    labels:
      - traefik.enable=true
      - traefik.tcp.routers.db-postgres.service=db-postgres
      - traefik.tcp.services.db-postgres.loadbalancer.server.port=5432
      - traefik.tcp.routers.db-postgres.rule=HostSNI(`${POSTGRES_HOST}`)
        # - traefik.tcp.routers.db-postgres.tls.passthrough=true
      - traefik.tcp.routers.db-postgres.entrypoints=postgres
      - traefik.tcp.routers.db-postgres.tls=true
      - traefik.tcp.routers.db-postgres.tls.certresolver=production
    env_file:
      - .env
    volumes:
      - ./database_volume:/var/lib/postgresql/data
    networks:
      - traefik_default

  adminer:
    image: adminer
    restart: always
    labels:
      # This service is discoverable
      - traefik.enable=true
      - traefik.http.services.adminer-postgres.loadBalancer.server.port=8080
      - traefik.http.routers.adminer-postgres.rule=Host(`${ADMINER_HOST}`)
      - traefik.http.routers.adminer-postgres.tls=true
      - traefik.http.routers.adminer-postgres.entrypoints=websecure
      - traefik.http.routers.adminer-postgres.tls.certresolver=production

    networks:
      - traefik_default

networks:
  traefik_default:
    external: true