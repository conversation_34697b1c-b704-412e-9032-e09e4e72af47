# Backend Deployment Guide

## 🚀 First Time Setup

If this is your first time running the project:

### 1. Start Docker Containers

Set the image tag you want to deploy, then start the containers:

```bash
tag=v.x.x.x docker compose up -d
```

Replace `v.x.x.x` with the version tag you want to deploy (e.g., `v1.0.0`, `v2.1.3`).

> **Note:**  
> The `docker-compose.yml` must support dynamic tag injection, e.g., `${tag}` in the image section.

---

### 2. Access the Application Container

Check running containers:

```bash
docker ps
```

Then access the app container (replace `<container-name>`):

```bash
docker exec -it <container-name> /bin/bash
```

---

### 3. Setup the Database

Once inside the container, run:

```bash
poe makemigrations
poe migrate
poe initdb
```

| Command | Purpose |
|:---|:---|
| `poe makemigrations` | Create migration files based on model changes. |
| `poe migrate` | Apply migrations to create or update the database schema. |
| `poe initdb` | (Optional) Initialize database with default/sample data. |

---

## 🛠 Requirements

- Docker
- Docker Compose
- Inside container: Poetry + Poe the Poet installed

---

## 🧹 Useful Docker Commands

| Command | Purpose |
|:---|:---|
| `docker compose down` | Stop and remove all containers. |
| `docker compose up -d --build` | Build and restart containers. |
| `docker logs <container-name>` | View container logs. |

---

## 📝 Notes

- Make sure `.env` file and environment variables are properly set before starting.
- If `poe` command is not found, ensure `poethepoet` is installed inside the Poetry environment.
- After first setup, next time you only need:

```bash
tag=v.x.x.x docker compose up -d
```

No need to re-run migrations unless models change.

---