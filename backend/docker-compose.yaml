services:
  backend:
    image: ghcr.io/ai-brainlab/salmate_backend:${tag}
    platform: linux/amd64
    env_file:
      - .env
    labels:
      # This service is discoverable
      - traefik.enable=true
      - traefik.http.services.backend-salmate.loadBalancer.server.port=8000
      - traefik.http.routers.backend-salmate.rule=Host(`${BACKEND_HOST}`)
      - traefik.http.routers.backend-salmate.tls=true
      - traefik.http.routers.backend-salmate.entrypoints=websecure
      - traefik.http.routers.backend-salmate.tls.certresolver=production
    networks:
      - traefik_default
    volumes:
      - ./logs:/src/app/logging
    restart: always
    
  celery-worker:
    image: ghcr.io/ai-brainlab/salmate_backend:${tag}
    platform: linux/amd64
    command: poe start_celery_worker  # This should match your dev command
    env_file:
      - .env
    depends_on:
      - backend
    networks:
      - traefik_default
    restart: always

  celery-beat:
    image: ghcr.io/ai-brainlab/salmate_backend:${tag}
    platform: linux/amd64
    command: poe start_celery_beat  # This should match your dev command
    env_file:
      - .env
    depends_on:
      - backend
    networks:
      - traefik_default
    restart: always

  celery-line:
    image: ghcr.io/ai-brainlab/salmate_backend:${tag}
    platform: linux/amd64
    command: poe start_celery_line  # This should match your dev command
    env_file:
      - .env
    depends_on:
      - backend
    networks:
      - traefik_default
    restart: always

  celery-websocket:
    image: ghcr.io/ai-brainlab/salmate_backend:${tag}
    platform: linux/amd64
    command: poe start_celery_websocket  # This should match your dev command
    env_file:
      - .env
    depends_on:
      - backend
    networks:
      - traefik_default
    restart: always

networks:
  traefik_default:
    external: true