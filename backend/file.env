# Backend Server
BACKEND_HOST=

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# Database Configuration
DB_HOST=db
DB_PORT=5432
DB_NAME=salmate
DB_USER=admin
DB_PASS=password

# LINE Messaging API (Fill in your LINE credentials)
LINE_ACCESS_TOKEN=
LINE_CHANNEL_SECRET=

# LangServe Configuration (Fill if using LangServe)
LANGSERVE_SCHEME=
LANGSERVE_HOST=
LANGSERVE_PORT=

# Allowed Hosts (Comma-separated)
ALLOWED_HOSTS=

# LLM Endpoints (Fill your own service URLs)
LLM_INTEND=
LLM_DEFAULT=
LLM_FAQ=
LLM_RECOMMENDATION=
SERVICE_VECTOR_DB=
LLM_SALMATE_LANGGRAPH=

# Ticket Settings
INACTIVE_TICKET_1ST_TIME_MINUTES=15
INACTIVE_TICKET_2ND_TIME_MINUTES=30
INACTIVE_TICKET_WAITING_TIME_FOR_CSAT_SCORE_MINUTES=5

# Celery Settings
CELERY_CHECK_INACTIVE_TICKET_PER_MINUTES=1
CELERY_CHECK_DATABACKUP_AT_HOUR=19

# Miscellaneous
NEARLY_EXPIRED_DATE=30

# Azure Blob Storage (Fill in Azure Storage details)
AZURE_ACCOUNT_NAME=
AZURE_ACCOUNT_KEY=
AZURE_CONTAINER=

# API URLs (Fill in your API endpoints)
ANALYSIS_API_URL=
VECTORDB_API_URL=
