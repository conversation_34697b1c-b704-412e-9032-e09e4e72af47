services:
  tool:
    image: ghcr.io/ai-brainlab/salmate-vector-db:${tag}
    platform: linux/amd64
    restart: always    
    labels:
      # This service is discoverable
      - traefik.enable=true
      - traefik.http.services.salmate-llm-vectordb.loadBalancer.server.port=8000
      - traefik.http.routers.salmate-llm-vectordb.rule=Host(`${VECTORDB_HOST}`)
      - traefik.http.routers.salmate-llm-vectordb.tls=true
      - traefik.http.routers.salmate-llm-vectordb.entrypoints=websecure
      - traefik.http.routers.salmate-llm-vectordb.tls.certresolver=production
    env_file:
      - .env
    secrets:
      - openai
    networks:
      - traefik_default

secrets:
  openai:
    file: .openai.key
networks:
  traefik_default:
    external: true