services:
  langgraph:
    image: ghcr.io/ai-brainlab/salmate-langgraph:${tag}
    platform: linux/amd64
    restart: always
    environment:
      # Here you can set the production number of workers, e.g., 8
      - WORKER_COUNT=16
    labels:
      # This service is discoverable
      - traefik.enable=true
      - traefik.http.services.salmate-langgraph.loadBalancer.server.port=8000
      - traefik.http.routers.salmate-langgraph.rule=Host(`${LANGGRAPH_HOST}`)
      - traefik.http.routers.salmate-langgraph.tls=true
      - traefik.http.routers.salmate-langgraph.entrypoints=websecure
      - traefik.http.routers.salmate-langgraph.tls.certresolver=production
    env_file:
      - .env
    secrets:
      - openai
    networks:
      - traefik_default

secrets:
  openai:
    file: .openai.key
networks:
  traefik_default:
    external: true