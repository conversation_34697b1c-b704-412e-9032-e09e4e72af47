services:
  service-name:
    image: ghcr.io/ai-brainlab/service-name::${tag}
    platform: linux/amd64
    restart: always
    labels:
      # This service is discoverable
      - traefik.enable=true
      - traefik.http.services.service-name:.loadBalancer.server.port=8000
      - traefik.http.routers.service-name:.rule=Host(`xxx.xxx.xxx.aibrainlab.co`)
      - traefik.http.routers.service-name:.tls=true
      - traefik.http.routers.service-name:.entrypoints=websecure
      - traefik.http.routers.service-name:.tls.certresolver=production
    env_file:
      - .env
    secrets:
      - openai
    networks:
      - traefik_default

secrets:
  openai:
    file: .openai.key
networks:
  traefik_default:
    external: true