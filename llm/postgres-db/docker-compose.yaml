services:
  pgvector-db:
    image: pgvector/pgvector:pg16
    restart: always
    # set shared memory limit when using docker-compose
    shm_size: 128mb
    # or set shared memory limit when deploy via swarm stack
    #volumes:
    #  - type: tmpfs
    #    target: /dev/shm
    #    tmpfs:
    #      size: 134217728 # 128*2^20 bytes = 128Mb
    # environment:
    #   POSTGRES_PASSWORD: example
    labels:
      - traefik.enable=true
      - traefik.tcp.routers.db-postgres-vectordb.service=db-postgres-vectordb
      - traefik.tcp.services.db-postgres-vectordb.loadbalancer.server.port=5432
      - traefik.tcp.routers.db-postgres-vectordb.rule=HostSNI(`${POSTGRES_DB_HOST}`)
        # - traefik.tcp.routers.db-postgres-vectordb.tls.passthrough=true
      - traefik.tcp.routers.db-postgres-vectordb.entrypoints=postgres
      - traefik.tcp.routers.db-postgres-vectordb.tls=true
      - traefik.tcp.routers.db-postgres-vectordb.tls.certresolver=production
    env_file:
      - .env
    volumes:
      - ./postgres-volume:/var/lib/postgresql/data
    networks:
      - traefik_default
volumes:
  postgres-volume:  # Define the named volume here
    
networks:
  traefik_default:
    external: true