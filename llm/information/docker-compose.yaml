services:
  faq:
    image: ghcr.io/ai-brainlab/salmate-llm-information:${tag}
    platform: linux/amd64
    restart: always
    labels:
      # This service is discoverable
      - traefik.enable=true
      - traefik.http.services.salmate-llm-information.loadBalancer.server.port=8000
      - traefik.http.routers.salmate-llm-information.rule=Host(`${INFORMATION_HOST}`)
      - traefik.http.routers.salmate-llm-information.tls=true
      - traefik.http.routers.salmate-llm-information.entrypoints=websecure
      - traefik.http.routers.salmate-llm-information.tls.certresolver=production
    env_file:
      - .env
    secrets:
      - openai
    networks:
      - traefik_default

secrets:
  openai:
    file: .openai.key
networks:
  traefik_default:
    external: true