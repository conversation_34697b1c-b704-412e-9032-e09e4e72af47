services:
  postgres_state:
    image: postgres:15
    restart: always
    labels:
      - traefik.enable=true
      - traefik.tcp.routers.postgres_state.service=postgres_state
      - traefik.tcp.services.postgres_state.loadbalancer.server.port=5432
      - traefik.tcp.routers.postgres_state.rule=HostSNI(`${POSTGRES_STATE_HOST}`)
        # - traefik.tcp.routers.postgres_state.tls.passthrough=true
      - traefik.tcp.routers.postgres_state.entrypoints=postgres
      - traefik.tcp.routers.postgres_state.tls=true
      - traefik.tcp.routers.postgres_state.tls.certresolver=production
    env_file:
      - .env
    volumes:
      - ./postgres-data:/var/lib/postgresql/data
    networks:
      - traefik_default


volumes:
  postgres-data:  # Define the named volume here
    
networks:
  traefik_default:
    external: true