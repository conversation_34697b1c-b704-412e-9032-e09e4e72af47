services:
  intent:
    image: ghcr.io/ai-brainlab/salmate-llm-intent:${tag}
    platform: linux/amd64
    restart: always    
    labels:
      # This service is discoverable
      - traefik.enable=true
      - traefik.http.services.salmate-llm-intent.loadBalancer.server.port=8000
      - traefik.http.routers.salmate-llm-intent.rule=Host(`${INTENT_HOST}`)
      - traefik.http.routers.salmate-llm-intent.tls=true
      - traefik.http.routers.salmate-llm-intent.entrypoints=websecure
      - traefik.http.routers.salmate-llm-intent.tls.certresolver=production
    env_file:
      - .env
    secrets:
      - openai
    networks:
      - traefik_default

secrets:
  openai:
    file: .openai.key
networks:
  traefik_default:
    external: true